import { useState, useEffect } from "react";
import { useUser } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Play,
  Users,
  Plus,
  Settings,
  LogOut,
  Copy,
  UserPlus,
  Crown,
  Calendar,
  Bug,
  ChevronDown,
  ChevronUp,
  MoreVertical,
  Edit,
  Trash2
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import CreateRoomDialog from "@/components/CreateRoomDialog";
import JoinRoomDialog from "@/components/JoinRoomDialog";
import RoomSettingsDialog from "@/components/RoomSettingsDialog";
import UserProfileButton from "@/components/UserProfileButton";
import { useRooms, useRoomManagement, RoomWithMembers } from "@/hooks/useRooms";
import { AuthDebugger } from "@/components/AuthDebugger";

const Dashboard = () => {
  const { user } = useUser();
  const { rooms, loading, refetch } = useRooms();
  const { deleteRoom } = useRoomManagement();
  const navigate = useNavigate();
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [showJoinRoom, setShowJoinRoom] = useState(false);
  const [showDebugger, setShowDebugger] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<RoomWithMembers | null>(null);
  const [showRoomSettings, setShowRoomSettings] = useState(false);

  const handleJoinRoom = (roomId: string) => {
    navigate(`/room/${roomId}`);
  };

  const handleCopyRoomCode = (roomCode: string) => {
    navigator.clipboard.writeText(roomCode);
    toast.success("Room code copied to clipboard!");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleOpenRoomSettings = (room: RoomWithMembers) => {
    setSelectedRoom(room);
    setShowRoomSettings(true);
  };

  const handleQuickDelete = async (room: RoomWithMembers) => {
    if (window.confirm(`Are you sure you want to delete "${room.name}"? This action cannot be undone.`)) {
      try {
        await deleteRoom(room.id);
        toast.success("Room deleted successfully!");
        refetch();
      } catch (error) {
        console.error("Error deleting room:", error);
        toast.error("Failed to delete room. Please try again.");
      }
    }
  };

  const handleRoomUpdated = () => {
    refetch();
  };

  const handleRoomDeleted = () => {
    refetch();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-stone-50 to-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-slate-300 border-t-slate-900 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your rooms...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-stone-50 to-neutral-50">
      {/* Header */}
      <header className="border-b border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-slate-800 to-stone-700 rounded-xl flex items-center justify-center shadow-lg">
                <Play className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">CineSync</h1>
                <p className="text-sm text-slate-600">Welcome back, {user?.firstName}!</p>
              </div>
            </div>
            <UserProfileButton />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="border-slate-200 hover:shadow-lg transition-shadow cursor-pointer" 
                onClick={() => setShowCreateRoom(true)}>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-slate-800 to-stone-700 rounded-xl flex items-center justify-center">
                  <Plus className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">Create New Room</h3>
                  <p className="text-sm text-slate-600">Start a new movie session with friends</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-slate-200 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => setShowJoinRoom(true)}>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-slate-800 to-stone-700 rounded-xl flex items-center justify-center">
                  <UserPlus className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">Join Room</h3>
                  <p className="text-sm text-slate-600">Enter a room code to join friends</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* My Rooms */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-slate-900">My Rooms</h2>
            <Badge variant="secondary" className="text-slate-600">
              {rooms.length} room{rooms.length !== 1 ? 's' : ''}
            </Badge>
          </div>

          {rooms.length === 0 ? (
            <Card className="border-slate-200">
              <CardContent className="p-12 text-center">
                <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-slate-400" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900 mb-2">No rooms yet</h3>
                <p className="text-slate-600 mb-6">Create your first room to start watching movies with friends</p>
                <Button onClick={() => setShowCreateRoom(true)} className="bg-slate-900 hover:bg-slate-800">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Room
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rooms.map((room) => (
                <Card key={room.id} className="border-slate-200 hover:shadow-lg transition-all duration-200 group">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-lg text-slate-900 group-hover:text-slate-700 transition-colors">
                          {room.name}
                        </CardTitle>
                        {room.user_role === "owner" && (
                          <Crown className="w-4 h-4 text-amber-500" />
                        )}
                      </div>
                      {room.user_role === "owner" && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleOpenRoomSettings(room)}>
                              <Settings className="w-4 h-4 mr-2" />
                              Room Settings
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleQuickDelete(room)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete Room
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                    <CardDescription className="flex items-center text-sm text-slate-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      Created {formatDate(room.created_at)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center text-sm text-slate-600">
                        <Users className="w-4 h-4 mr-1" />
                        {room.member_count} member{room.member_count !== 1 ? 's' : ''}
                      </div>
                      <Badge variant={room.user_role === "owner" ? "default" : "secondary"}>
                        {room.user_role === "owner" ? "Owner" : "Member"}
                      </Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleJoinRoom(room.id)}
                        className="flex-1 bg-slate-900 hover:bg-slate-800"
                        size="sm"
                      >
                        <Play className="w-4 h-4 mr-2" />
                        Enter Room
                      </Button>
                      <Button
                        onClick={() => handleCopyRoomCode(room.code)}
                        variant="outline"
                        size="sm"
                        className="border-slate-300"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Debug Section */}
        <div className="mb-8">
          <Card className="border-slate-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bug className="w-5 h-5 text-slate-600" />
                  <CardTitle className="text-lg text-slate-900">Authentication Debugger</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDebugger(!showDebugger)}
                  className="text-slate-600 hover:text-slate-900"
                >
                  {showDebugger ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                </Button>
              </div>
              <CardDescription>
                Test your Clerk + Supabase authentication setup
              </CardDescription>
            </CardHeader>
            {showDebugger && (
              <CardContent className="pt-0">
                <AuthDebugger />
              </CardContent>
            )}
          </Card>
        </div>
      </main>

      {/* Dialogs */}
      <CreateRoomDialog 
        open={showCreateRoom} 
        onOpenChange={setShowCreateRoom}
        onRoomCreated={(roomId) => {
          setShowCreateRoom(false);
          refetch(); // Refresh the rooms list
          navigate(`/room/${roomId}`);
        }}
      />

      <JoinRoomDialog
        open={showJoinRoom}
        onOpenChange={setShowJoinRoom}
        onRoomJoined={(roomId) => {
          setShowJoinRoom(false);
          refetch(); // Refresh the rooms list
          navigate(`/room/${roomId}`);
        }}
      />

      {/* Room Settings Dialog */}
      {selectedRoom && (
        <RoomSettingsDialog
          open={showRoomSettings}
          onOpenChange={setShowRoomSettings}
          room={selectedRoom}
          onRoomUpdated={handleRoomUpdated}
          onRoomDeleted={handleRoomDeleted}
        />
      )}
    </div>
  );
};

export default Dashboard;
