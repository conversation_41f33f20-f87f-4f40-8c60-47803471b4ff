import { useState, useEffect } from "react";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { 
  Loader2, 
  Settings, 
  Trash2, 
  UserCheck, 
  Crown,
  AlertTriangle 
} from "lucide-react";
import { toast } from "sonner";
import { useRoomManagement, RoomWithMembers } from "@/hooks/useRooms";

interface RoomSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  room: RoomWithMembers;
  onRoomUpdated: () => void;
  onRoomDeleted: () => void;
}

const RoomSettingsDialog = ({ 
  open, 
  onOpenChange, 
  room, 
  onRoomUpdated, 
  onRoomDeleted 
}: RoomSettingsDialogProps) => {
  const [roomName, setRoomName] = useState("");
  const [roomDescription, setRoomDescription] = useState("");
  const [selectedNewOwner, setSelectedNewOwner] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showTransferConfirm, setShowTransferConfirm] = useState(false);
  
  const { updateRoom, deleteRoom, transferOwnership, loading } = useRoomManagement();

  // Initialize form with room data
  useEffect(() => {
    if (room) {
      setRoomName(room.name);
      setRoomDescription(room.description || "");
    }
  }, [room]);

  const handleUpdateRoom = async () => {
    if (!roomName.trim()) {
      toast.error("Please enter a room name");
      return;
    }

    try {
      await updateRoom(room.id, roomName.trim(), roomDescription.trim() || undefined);
      toast.success("Room updated successfully!");
      onRoomUpdated();
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating room:", error);
      toast.error("Failed to update room. Please try again.");
    }
  };

  const handleDeleteRoom = async () => {
    try {
      await deleteRoom(room.id);
      toast.success("Room deleted successfully!");
      onRoomDeleted();
      setShowDeleteConfirm(false);
      onOpenChange(false);
    } catch (error) {
      console.error("Error deleting room:", error);
      toast.error("Failed to delete room. Please try again.");
    }
  };

  const handleTransferOwnership = async () => {
    if (!selectedNewOwner) {
      toast.error("Please select a new owner");
      return;
    }

    try {
      await transferOwnership(room.id, selectedNewOwner);
      toast.success("Ownership transferred successfully!");
      onRoomUpdated();
      setShowTransferConfirm(false);
      onOpenChange(false);
    } catch (error) {
      console.error("Error transferring ownership:", error);
      toast.error("Failed to transfer ownership. Please try again.");
    }
  };

  const handleClose = () => {
    if (!loading) {
      // Reset form
      setRoomName(room.name);
      setRoomDescription(room.description || "");
      setSelectedNewOwner("");
      onOpenChange(false);
    }
  };

  // Get members who can become owners (exclude current owner)
  const eligibleMembers = room.members.filter(member => 
    member.user_id !== room.owner_id && member.role === 'member'
  );

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-slate-900">
              <Settings className="w-5 h-5" />
              Room Settings
            </DialogTitle>
            <DialogDescription className="text-slate-600">
              Manage your room settings, transfer ownership, or delete the room.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-6 py-4">
            {/* Room Details Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-slate-900">Room Details</h3>
              
              <div className="grid gap-2">
                <Label htmlFor="room-name" className="text-slate-700">
                  Room Name *
                </Label>
                <Input
                  id="room-name"
                  placeholder="e.g., Friday Movie Night"
                  value={roomName}
                  onChange={(e) => setRoomName(e.target.value)}
                  className="border-slate-300 focus:border-slate-500"
                  disabled={loading}
                  maxLength={50}
                />
                <p className="text-xs text-slate-500">
                  {roomName.length}/50 characters
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="room-description" className="text-slate-700">
                  Description
                </Label>
                <Textarea
                  id="room-description"
                  placeholder="Optional description for your room"
                  value={roomDescription}
                  onChange={(e) => setRoomDescription(e.target.value)}
                  className="border-slate-300 focus:border-slate-500 min-h-[80px]"
                  disabled={loading}
                  maxLength={200}
                />
                <p className="text-xs text-slate-500">
                  {roomDescription.length}/200 characters
                </p>
              </div>

              <Button 
                onClick={handleUpdateRoom}
                disabled={loading || !roomName.trim()}
                className="w-full bg-slate-900 hover:bg-slate-800"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Room"
                )}
              </Button>
            </div>

            <Separator />

            {/* Transfer Ownership Section */}
            {eligibleMembers.length > 0 && (
              <>
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-slate-900">Transfer Ownership</h3>
                  
                  <div className="grid gap-2">
                    <Label className="text-slate-700">
                      Select New Owner
                    </Label>
                    <Select value={selectedNewOwner} onValueChange={setSelectedNewOwner}>
                      <SelectTrigger className="border-slate-300">
                        <SelectValue placeholder="Choose a member to transfer ownership to" />
                      </SelectTrigger>
                      <SelectContent>
                        {eligibleMembers.map((member) => (
                          <SelectItem key={member.id} value={member.user_id}>
                            <div className="flex items-center gap-2">
                              <UserCheck className="w-4 h-4" />
                              {member.user_id}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={() => setShowTransferConfirm(true)}
                    disabled={loading || !selectedNewOwner}
                    variant="outline"
                    className="w-full border-amber-300 text-amber-700 hover:bg-amber-50"
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Transfer Ownership
                  </Button>
                </div>

                <Separator />
              </>
            )}

            {/* Danger Zone */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-red-600">Danger Zone</h3>
              
              <Button 
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loading}
                variant="destructive"
                className="w-full"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Room
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={handleClose}
              disabled={loading}
              className="border-slate-300"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="w-5 h-5" />
              Delete Room
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{room.name}"? This action cannot be undone. 
              All room data, messages, and member information will be permanently deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteRoom}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Room"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Transfer Ownership Confirmation Dialog */}
      <AlertDialog open={showTransferConfirm} onOpenChange={setShowTransferConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-amber-600">
              <Crown className="w-5 h-5" />
              Transfer Ownership
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to transfer ownership of "{room.name}" to the selected member? 
              You will become a regular member and lose owner privileges.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleTransferOwnership}
              disabled={loading}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Transferring...
                </>
              ) : (
                "Transfer Ownership"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RoomSettingsDialog;
