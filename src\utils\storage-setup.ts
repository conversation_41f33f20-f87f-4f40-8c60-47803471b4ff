import { supabase } from '@/lib/supabase';

export const ensureVideosBucket = async () => {
  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return false;
    }

    const videosBucket = buckets?.find(bucket => bucket.id === 'videos');
    
    if (!videosBucket) {
      // Create the bucket if it doesn't exist
      const { data, error } = await supabase.storage.createBucket('videos', {
        public: true,
        fileSizeLimit: 104857600, // 100MB
        allowedMimeTypes: [
          'video/mp4',
          'video/webm', 
          'video/ogg',
          'video/avi',
          'video/mov',
          'video/wmv',
          'video/flv',
          'video/mkv'
        ]
      });

      if (error) {
        console.error('Error creating videos bucket:', error);
        return false;
      }

      console.log('Videos bucket created successfully');
    }

    return true;
  } catch (error) {
    console.error('Error ensuring videos bucket:', error);
    return false;
  }
};
