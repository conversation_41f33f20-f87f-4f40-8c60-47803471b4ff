import { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { supabase, Room, RoomMember, RoomInvitation } from '@/lib/supabase';
import { toast } from 'sonner';

export interface RoomWithMembers extends Room {
  members: RoomMember[];
  member_count: number;
  user_role?: 'owner' | 'member';
}

export const useRooms = () => {
  const { user } = useUser();
  const [rooms, setRooms] = useState<RoomWithMembers[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchUserRooms = async () => {
    if (!user?.id) {
      setLoading(false);
      setRooms([]);
      return;
    }

    try {
      setLoading(true);

      // Get rooms where user is a member
      const { data: memberData, error: memberError } = await supabase
        .from('room_members')
        .select(`
          room_id,
          role,
          rooms (
            id,
            name,
            description,
            code,
            owner_id,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id);

      if (memberError) throw memberError;

      // Handle case where user has no rooms (memberData is null or empty)
      if (!memberData || memberData.length === 0) {
        setRooms([]);
        setLoading(false);
        return;
      }

      // Get member counts for each room
      const roomIds = memberData.map((m: any) => m.room_id);
      const { data: memberCounts, error: countError } = await supabase
        .from('room_members')
        .select('room_id')
        .in('room_id', roomIds);

      if (countError) throw countError;

      // Process the data
      const roomsWithMembers: RoomWithMembers[] = memberData.map((member: any) => {
        const room = member.rooms as Room;
        const memberCount = memberCounts?.filter((m: any) => m.room_id === room.id).length || 0;

        return {
          ...room,
          members: [],
          member_count: memberCount,
          user_role: member.role
        };
      });

      setRooms(roomsWithMembers);
    } catch (error) {
      console.error('Error fetching rooms:', error);
      toast.error('Failed to load rooms');
      setRooms([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch rooms if we have a user ID, otherwise set loading to false
    if (user?.id) {
      fetchUserRooms();
    } else {
      setLoading(false);
      setRooms([]);
    }
  }, [user?.id]);

  return {
    rooms,
    loading,
    refetch: fetchUserRooms
  };
};

export const useCreateRoom = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const createRoom = async (name: string, description?: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Generate a unique room code
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();

      // Create the room
      const { data: room, error: roomError } = await supabase
        .from('rooms')
        .insert({
          name,
          description,
          code,
          owner_id: user.id
        })
        .select()
        .single();

      if (roomError) throw roomError;

      // Add the creator as the owner member
      const { error: memberError } = await supabase
        .from('room_members')
        .insert({
          room_id: room.id,
          user_id: user.id,
          role: 'owner'
        });

      if (memberError) throw memberError;

      return room;
    } finally {
      setLoading(false);
    }
  };

  return {
    createRoom,
    loading
  };
};

export const useJoinRoom = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const joinRoom = async (code: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Find the room by code
      const { data: room, error: roomError } = await supabase
        .from('rooms')
        .select('*')
        .eq('code', code.toUpperCase())
        .single();

      if (roomError || !room) {
        throw new Error('Room not found');
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', room.id)
        .eq('user_id', user.id)
        .single();

      if (existingMember) {
        return room; // Already a member
      }

      // Add user as a member
      const { error: memberError } = await supabase
        .from('room_members')
        .insert({
          room_id: room.id,
          user_id: user.id,
          role: 'member'
        });

      if (memberError) throw memberError;

      return room;
    } finally {
      setLoading(false);
    }
  };

  return {
    joinRoom,
    loading
  };
};

export const useRoom = (roomId: string) => {
  const { user } = useUser();
  const [room, setRoom] = useState<RoomWithMembers | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchRoom = async () => {
    if (!user?.id || !roomId) return;

    try {
      setLoading(true);

      // Get room details and user's membership
      const { data: memberData, error: memberError } = await supabase
        .from('room_members')
        .select(`
          role,
          rooms (
            id,
            name,
            description,
            code,
            owner_id,
            created_at,
            updated_at
          )
        `)
        .eq('room_id', roomId)
        .eq('user_id', user.id)
        .single();

      if (memberError || !memberData) {
        throw new Error('Room not found or access denied');
      }

      // Get all room members
      const { data: allMembers, error: membersError } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', roomId);

      if (membersError) throw membersError;

      const roomData = Array.isArray(memberData.rooms) ? memberData.rooms[0] : memberData.rooms;
      const roomWithMembers: RoomWithMembers = {
        ...(roomData as Room),
        members: allMembers || [],
        member_count: allMembers?.length || 0,
        user_role: memberData.role
      };

      setRoom(roomWithMembers);
    } catch (error) {
      console.error('Error fetching room:', error);
      toast.error('Failed to load room');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoom();
  }, [user?.id, roomId]);

  return {
    room,
    loading,
    refetch: fetchRoom
  };
};

export const useRoomManagement = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const updateRoom = async (roomId: string, name: string, description?: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      const { data: room, error } = await supabase
        .from('rooms')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('owner_id', user.id) // Ensure only owner can update
        .select()
        .single();

      if (error) throw error;
      return room;
    } finally {
      setLoading(false);
    }
  };

  const deleteRoom = async (roomId: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('id', roomId)
        .eq('owner_id', user.id); // Ensure only owner can delete

      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const transferOwnership = async (roomId: string, newOwnerId: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Check if new owner is a member
      const { data: newOwnerMember, error: memberError } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', roomId)
        .eq('user_id', newOwnerId)
        .single();

      if (memberError || !newOwnerMember) {
        throw new Error('New owner must be a member of the room');
      }

      // Update room owner
      const { error: updateRoomError } = await supabase
        .from('rooms')
        .update({
          owner_id: newOwnerId,
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('owner_id', user.id); // Ensure only current owner can transfer

      if (updateRoomError) throw updateRoomError;

      // Update old owner role to member
      const { error: updateOldOwnerError } = await supabase
        .from('room_members')
        .update({ role: 'member' })
        .eq('room_id', roomId)
        .eq('user_id', user.id);

      if (updateOldOwnerError) throw updateOldOwnerError;

      // Update new owner role to owner
      const { error: updateNewOwnerError } = await supabase
        .from('room_members')
        .update({ role: 'owner' })
        .eq('room_id', roomId)
        .eq('user_id', newOwnerId);

      if (updateNewOwnerError) throw updateNewOwnerError;
    } finally {
      setLoading(false);
    }
  };

  return {
    updateRoom,
    deleteRoom,
    transferOwnership,
    loading
  };
};
